const jwt = require('jsonwebtoken');

module.exports = (req,res,next) =>{
  const token = req.cookies.token || (req.headers.authorization && req.headers.authorization.split(' ')[1]);
  if(!token) return res.status(401).json({error: 'Unauthorized - No token provided'});
  try {
    const decoded = jwt.verify(token,process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({error:'Invalid or expired token'});
  }
  
}