import {createSlice} from '@reduxjs/toolkit';

const initialState = {user: null, token:null};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers:{
    setCredentials:(state,action)=>{
      state.user = action.payload.user;
      state.token = action.payload.token;
    },
    logout:(state) => initialState,
  },
});

export const {setCredentials,logout} = authSlice.actions;
export default authSlice.reducer;
